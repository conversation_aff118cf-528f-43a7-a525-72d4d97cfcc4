package device

import (
	"encoding/json"
	"net/http"
	"runtime"
	"time"

	"git.uozi.org/uozi/potato-api/internal/analytics"
	"git.uozi.org/uozi/potato-api/internal/device"
	"git.uozi.org/uozi/potato-api/internal/grpc"
	"git.uozi.org/uozi/potato-api/internal/grpc/proto"
	"git.uozi.org/uozi/potato-api/internal/helper"
	"git.uozi.org/uozi/potato-api/model"
	"git.uozi.org/uozi/potato-api/query"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/logger"
)

// Copy from github.com/gorilla/websocket
const (
	// TextMessage denotes a text data message. The text message payload is
	// interpreted as UTF-8 encoded text data.
	TextMessage = 1

	// BinaryMessage denotes a binary data message.
	BinaryMessage = 2

	// CloseMessage denotes a close control message. The optional message
	// payload contains a numeric code and text. Use the FormatCloseMessage
	// function to format a close message payload.
	CloseMessage = 8

	// PingMessage denotes a ping control message. The optional message payload
	// is UTF-8 encoded text.
	PingMessage = 9

	// PongMessage denotes a pong control message. The optional message payload
	// is UTF-8 encoded text.
	PongMessage = 10
)

var AudioParams = &proto.AudioParams{
	Format:        "opus",
	SampleRate:    16000,
	Channels:      1,
	FrameDuration: 60,
}

// Live 与硬件通讯的总线接口
func Live(c *gin.Context) {
	time1 := time.Now().UnixMilli()

	logger.Info("websocket尝试连接")
	var upgrader = &websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	ws, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.Error(err)
		return
	}
	defer func() {
		if ws != nil {
			ws.Close()
			logger.Debug("ws关闭")
		}
	}()

	var helloFrame device.WebSocketMessagePayload
	err = ws.ReadJSON(&helloFrame)
	if err != nil {
		if helper.IsUnexpectedWebsocketError(err) {
			logger.Error(err)
			return
		}
		return
	}

	if helloFrame.Type != "hello" {
		err = ws.WriteJSON(&device.WebSocketMessagePayload{
			Type: "error",
			Text: "invalid type",
		})
		if err != nil {
			if helper.IsUnexpectedWebsocketError(err) {
				logger.Error(err)
				return
			}
			return
		}
		return
	}
	logger.Info("helloFrame.deviceinfo.mac: ", helloFrame.DeviceInfo.MAC)
	if helloFrame.DeviceInfo == nil {
		err = ws.WriteJSON(&device.WebSocketMessagePayload{
			Type: "error",
			Text: "device info not found",
		})
		if err != nil {
			if helper.IsUnexpectedWebsocketError(err) {
				logger.Error(err)
				return
			}
			return
		}
		return
	}
	qia := query.IntelligentAgent
	d := query.Device
	deviceModel, err := d.Where(d.MAC.Eq(helloFrame.DeviceInfo.MAC)).First()

	if err != nil {
		err = ws.WriteJSON(&device.WebSocketMessagePayload{
			Type: "error",
			Text: "device not found",
		})
		logger.Error(err)
		return
	}

	// 如果设备系统版本相同，则更新数据库里的设备系统版本
	if deviceModel.SystemVersion != helloFrame.DeviceInfo.SystemVersion {
		d.Where(d.ID.Eq(deviceModel.ID)).Update(d.SystemVersion, helloFrame.DeviceInfo.SystemVersion)
	}
	d.Where(d.ID.Eq(deviceModel.ID)).Update(d.Identifier, helloFrame.DeviceInfo.Identifier)

	// 如果设备已经注册，则设置注册状态,在白名单中，但是没有被用户绑定，说明依然没有被注册
	helloFrame.DeviceInfo.Device = deviceModel
	helloFrame.DeviceInfo.Register = deviceModel.UserId > 0

	frame := &device.WebSocketFrame{
		Conn:        ws,
		MessageType: websocket.TextMessage,
		Data:        &helloFrame,
		Context:     &helloFrame,
	}
	// 验证hello握手返回
	var agent *model.IntelligentAgent
	if helloFrame.DeviceInfo.Register {
		agent, err = qia.Where(qia.ID.Eq(deviceModel.IntelligentAgentID)).First()
		frame.Data.StatusCode = 200

		handler, ok := device.GetHandler("hello")
		if ok {
			handler(frame)
		} else {
			logger.Error("hello处理函数未找到")
		}

		if err != nil {
			var str string
			logger.Error("搜素设备的智能体失败:", err)
			handler, ok := device.GetHandler("tts_error")
			if !ok {
				str = ("tts_error处理函数未找到")
				return
			}
			str = "当前设备未绑定角色，请先绑定角色后重启进行对话"
			frame.Data.Text = str
			handler(frame)
			handler, ok = device.GetHandler("end_chat")
			if !ok {
				str = ("end_chat处理函数未找到")
				logger.Error(str)
				return
			}
			handler(frame)
			return
		}
	} else {
		handler, ok := device.GetHandler("register")
		if ok {
			handler(frame)
		}
		return
	}

	GrpcClient, err := grpc.Ping(frame)
	if err != nil {
		logger.Error("ping失败:", err)
		if GrpcClient != nil {
			logger.Error("defer前,GrpcClient关闭")
			GrpcClient.Close()
		}
		return
	}
	// 此时defer还没有被加载到堆栈中，可能需要手动关闭grpc的资源
	if GrpcClient == nil {
		logger.Error("grpc client is nil")
		return
	}

	sessionId_ := GrpcClient.GetContext().Value("sessionid")
	sessionId := sessionId_.(string)

	chat := getChat(deviceModel, sessionId)
	agentList := GetAgentList(deviceModel.UserId)
	// logger.Debug("agentList:", agentList)
	// websocket和grpc的连接的会话管理
	sessionCtx := &grpc.SessionContext{
		WebSocketFrame: frame,
		UserOpusFrames: make([][]byte, 0),
		Device:         deviceModel,
		Chat:           chat,
		GrpcClient:     GrpcClient,
		Agent:          *agent,
		AgentList:      agentList,
		DeviceState:    &model.DeviceState{},
		LastActive:     uint64(time.Now().Unix()),
	}

	// 更新会话上下文中的gRPC客户端引用
	sessionCtx.GrpcClient = GrpcClient
	GrpcClient.SetSessionID(sessionId)
	// 存储到全局会话映射
	grpc.SessionMap.Store(sessionId, sessionCtx)
	// 添加到正在对话的设备列表
	analytics.AddChatingDevice(deviceModel.ID)

	// 在连接关闭时清理
	defer func() {
		qia.Where(qia.ID.Eq(chat.AgentID)).Update(qia.LastChat, time.Now().Unix())
		if sessionCtx.SpeakerId != "" {
			sessionCtx.GrpcClient.CallBackUpdateMemory(sessionCtx)
			sessionCtx.GrpcClient.CallBackConversationSummary(sessionCtx)
			sessionCtx.GrpcClient.CallBackSaveConversation4RAG(sessionCtx)
		}

		logger.Debug("清理前defer goroutine count:", runtime.NumGoroutine())
		// 从正在对话的设备列表中移除
		analytics.RemoveChatingDevice(deviceModel.ID)

		sessionCtx.UserOpusFrames = nil
		grpc.SessionMap.Delete(sessionId)
		if GrpcClient != nil {
			GrpcClient.Close()
		}
		logger.Info("grpc关闭")
		logger.Debug("会话已清理:", sessionId)
		logger.Debug("清理后defer goroutine count:", runtime.NumGoroutine())
	}()
	// 主动断开websocket连接是否会被服务端发现，现在发现主动断开websocket无法释放资源
	time2 := time.Now().UnixMilli()
	logger.Info("初始化耗时->", time2-time1)
	for {
		// logger.Debug("进入for goroutine count:", runtime.NumGoroutine())
		messageType, message, err := ws.ReadMessage()
		if err != nil {
			if helper.IsUnexpectedWebsocketError(err) {
				logger.Error(err)
				return
			}
			return
		}
		frame.Message = message
		if messageType == websocket.TextMessage {
			frame.Data = &device.WebSocketMessagePayload{}
			err = json.Unmarshal(message, frame.Data)
			if err != nil {
				logger.Error(err)
				return
			}
			switch frame.Data.Type {
			case "listen":
				if frame.Data.State == "start" {
					GrpcClient.GrpcSendAudioControl("START")
					logger.Debug("websocket通道传来start类型数据")
					// 从mac找到userid，从userid找到声纹配置
					voiceprints := device.GetUserVoiceprint(sessionCtx.Device.UserId)
					GrpcClient.GrpcSendAudioConfig(AudioParams, voiceprints)
				} else if frame.Data.State == "stop" {
					GrpcClient.GrpcSendAudioControl("FINISH")
					logger.Info("websocket通道传来stop类型数据")
					// 关闭流资源
				}
			case "iot":
				logger.Debug("websocket通道传来iot类型数据")
				for _, value := range frame.Data.States {
					if value.Name == "Speaker" {
						volume := value.State["volume"]
						sessionCtx.DeviceState.Volume = cast.ToFloat64(volume)
					}
					if value.Name == "Battery" {
						level := value.State["level"]
						sessionCtx.DeviceState.Level = cast.ToFloat64(level)
						charging := value.State["charging"]
						sessionCtx.DeviceState.Charging = cast.ToBool(charging)
					}
				}
			case "abort":
				sessionCtx.GrpcClient.IsReturnWsPause = true
				logger.Info("websocket通道传来abort类型数据")
			case "ota":
				logger.Info("websocket通道传来 ota 类型数据")
				otaHandler, ok := device.GetHandler("ota")
				if !ok {
					logger.Error("ota处理函数未找到")
					continue
				}
				otaHandler(frame)
				continue
			default:
				logger.Warn("websocket通道传来未知类型数据")
				continue
			}
		} else if messageType == websocket.BinaryMessage {
			// 在 BinaryMessage 处理部分：
			frame.Data = &device.WebSocketMessagePayload{}
			// 直接发送当前二进制消息到 gRPC
			sessionCtx.UserOpusFrames = append(sessionCtx.UserOpusFrames, frame.Message)
			// logger.Debug(len(sessionCtx.UserOpusFrames))
			err := GrpcClient.SendAudioData(frame.Message)
			if err != nil {
				logger.Error("发送音频数据失败:", err)
				return
			}
		}
	}
}
func getChat(device *model.Device, sessionId string) *model.Chat {
	chat := &model.Chat{
		SessionID: sessionId,
		DeviceID:  device.ID,
		UserID:    device.UserId,
		AgentID:   device.IntelligentAgentID,
	}
	return chat
}

func GetAgentList(userid uint64) []*proto.RoleList {
	qia := query.IntelligentAgent
	var roleList []*proto.RoleList
	agentList, err := qia.Where(qia.UserID.Eq(userid)).Find()
	if err != nil {
		logger.Error("搜素用户的智能体列表失败:")
		return nil
	}
	model.IntelligentAgents(agentList).OverrideByRoleTemplate()

	for _, agent := range agentList {
		// TODO:当roleTempPlateId不为0时，agent的name=roleTempPlate的name
		role := &proto.RoleList{
			Id:   agent.ID,
			Name: agent.Name,
		}
		roleList = append(roleList, role)
	}
	logger.Debug("智能体列表为：", roleList)
	return roleList
}
func InitRouter(r *gin.Engine) {
	r.GET("/devices/live", Live)
}
